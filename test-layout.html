<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Layout - Painel Lateral</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            overflow: hidden;
            display: flex;
            height: 100vh;
        }

        #config-panel {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            width: 350px;
            background: white;
            border-right: 2px solid #0d6efd;
            padding: 15px 20px;
            z-index: 2000;
            box-shadow: 5px 0 20px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            gap: 20px;
            font-size: 12px;
            overflow-y: auto;
            transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;
        }

        #config-panel.minimized {
            left: -320px;
        }

        .minimize-button {
            position: fixed;
            top: 15px;
            left: 325px;
            background: #0d6efd;
            color: white; 
            border: none; 
            border-radius: 3px;
            width: 25px; 
            height: 25px; 
            font-size: 14px; 
            line-height: 1;
            cursor: pointer; 
            z-index: 2001; 
            display: flex; 
            align-items: center; 
            justify-content: center;
            transition: all 0.3s ease;
        }

        .minimize-button:hover { 
            background: #0b5ed7; 
        }

        body:has(#config-panel.minimized) .minimize-button {
            left: 5px;
        }

        #diagram-container {
            position: absolute;
            width: 2400px;
            height: 900px;
            background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
            left: 370px;
            top: 15px;
            transition: left 0.3s ease-in-out;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #333;
        }

        body:has(#config-panel.minimized) #diagram-container {
            left: 30px;
        }

        .panel-column {
            display: flex;
            flex-direction: column;
            gap: 12px;
            width: 100%;
            margin-bottom: 20px;
        }

        .panel-column h3 {
            margin: 0 0 3px 0;
            color: #2b2d42;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 8px;
        }

        .config-row {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .config-row label {
            font-weight: 600;
            color: #495057;
            font-size: 12px;
        }

        .config-row select, .config-row input {
            padding: 8px 9px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 12px;
            background: #f8f9fa;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .action-button {
            padding: 8px 12px;
            background: #0d6efd;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 5px;
            width: 100%;
        }

        .action-button:hover {
            background: #0b5ed7;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        #logo-container {
            position: fixed;
            top: 15px;
            left: 370px;
            z-index: 2000;
            transition: left 0.3s ease-in-out;
            background: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        body:has(#config-panel.minimized) #logo-container {
            left: 50px;
        }

        #total-power-display {
            position: fixed;
            top: 15px;
            right: 20px;
            z-index: 2000;
            background: white;
            padding: 8px 15px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 14px;
            color: #2b2d42;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div id="total-power-display">Potência: 15.2 MVAr</div>
    <div id="logo-container">
        <div style="width: 40px; height: 40px; background: #0d6efd; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">LOGO</div>
    </div>

    <button id="toggle-panel-button" class="minimize-button">◀</button>

    <div id="config-panel">
        
        <div class="panel-column">
            <h3>Configurações Gerais</h3>
            <div class="config-row">
                <label for="circuit-type">Tipo de Circuito</label>
                <select id="circuit-type">
                    <option value="monofasico">Monofásico</option>
                    <option value="trifasico" selected>Trifásico</option>
                </select>
            </div>
            <div class="config-row">
                <label for="bank-voltage">Tensão Nominal (kV)</label>
                <select id="bank-voltage">
                    <option value="13.8">13.8</option>
                    <option value="27.6">27.6</option>
                    <option value="41.4">41.4</option>
                </select>
            </div>
        </div>

        <div class="panel-column">
            <h3>Cálculo de Potência</h3>
            <div class="config-row">
                <label for="test-power">Potência Reativa (MVAr)</label>
                <input type="number" id="test-power" placeholder="Ex: 5.2" step="0.1">
            </div>
            <button class="action-button">Calcular Configuração</button>
        </div>

        <div class="panel-column">
            <h3>Controles do Diagrama</h3>
            <div class="config-row">
                <button class="action-button">Resetar Diagrama</button>
            </div>
            <div class="config-row">
                <button class="action-button">Zoom +</button>
            </div>
            <div class="config-row">
                <button class="action-button">Zoom -</button>
            </div>
        </div>
    </div>

    <div id="diagram-container">
        <div>
            <h2>🔌 DIAGRAMA UNIFILAR - BANCO DE CAPACITORES</h2>
            <p>Esta é a área principal do diagrama.</p>
            <p>O painel lateral pode ser minimizado clicando na seta ◀</p>
            <p>Quando minimizado, o diagrama ocupa mais espaço na tela.</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const configPanel = document.getElementById('config-panel');
            const togglePanelButton = document.getElementById('toggle-panel-button');

            togglePanelButton.addEventListener('click', () => {
                configPanel.classList.toggle('minimized');
                if (configPanel.classList.contains('minimized')) {
                    togglePanelButton.textContent = '▶';
                } else {
                    togglePanelButton.textContent = '◀';
                }
            });
        });
    </script>
</body>
</html>
