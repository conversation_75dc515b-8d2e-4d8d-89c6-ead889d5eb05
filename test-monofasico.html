<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Modo Monofásico</title>
</head>
<body>
    <h1>Teste do Cálculo Monofásico</h1>
    
    <div>
        <label>Tipo de Circuito:</label>
        <select id="circuit-type">
            <option value="monofasico" selected>Monofásico</option>
            <option value="trifasico">Trifásico</option>
        </select>
    </div>
    
    <div>
        <label>Tensão (kV):</label>
        <select id="bank-voltage">
            <option value="13.8">13.8</option>
            <option value="27.6">27.6</option>
            <option value="41.4">41.4</option>
            <option value="55.2">55.2</option>
        </select>
    </div>
    
    <div>
        <label>Potência Reativa (MVAr):</label>
        <input type="number" id="test-power" value="5" step="0.1">
    </div>
    
    <div>
        <label>Tensão de Ensaio (kV) - Opcional:</label>
        <input type="number" id="test-voltage" step="0.1">
    </div>
    
    <div>
        <label>Potência Efetiva (MVAr):</label>
        <input type="number" id="effective-test-power" readonly>
    </div>
    
    <button id="calculate-circuit">Calcular</button>
    
    <div id="circuit-results"></div>
    
    <script src="bank-selector.js"></script>
    <script>
        // Teste simples
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Iniciando teste...');
            
            // Criar instância do BankSelector
            const bankSelector = new BankSelector();
            console.log('BankSelector criado:', bankSelector);
            
            // Testar função _getSinglePhaseColumns
            console.log('Testando _getSinglePhaseColumns para 13.8kV:');
            const columns13_8 = bankSelector._getSinglePhaseColumns(13.8);
            console.log('Colunas 13.8kV:', columns13_8);
            
            console.log('Testando _getSinglePhaseColumns para 27.6kV:');
            const columns27_6 = bankSelector._getSinglePhaseColumns(27.6);
            console.log('Colunas 27.6kV:', columns27_6);
            
            // Testar cálculo completo
            const testInputs = {
                circuitType: 'monofasico',
                bankVoltageKV: 13.8,
                targetQ_MVAR: 5
            };
            
            console.log('Testando cálculo completo com inputs:', testInputs);
            const result = bankSelector.findBestBankConfiguration(testInputs);
            console.log('Resultado:', result);
            
            // Adicionar resultado na página
            const resultsDiv = document.getElementById('circuit-results');
            if (result.error) {
                resultsDiv.innerHTML = `<p style="color: red;">Erro: ${result.error}</p>`;
            } else {
                resultsDiv.innerHTML = `<p style="color: green;">Sucesso! Encontradas ${result.solutions.length} soluções.</p>`;
                result.solutions.forEach((sol, i) => {
                    resultsDiv.innerHTML += `<p>Solução ${i+1}: ${sol.q_config_str} - ${sol.q_efetiva.toFixed(2)} MVAr</p>`;
                });
            }
        });
        
        // Configurar botão de cálculo
        document.getElementById('calculate-circuit').addEventListener('click', () => {
            const bankSelector = new BankSelector();
            const inputs = {
                circuitType: document.getElementById('circuit-type').value,
                bankVoltageKV: parseFloat(document.getElementById('bank-voltage').value),
                targetQ_MVAR: parseFloat(document.getElementById('test-power').value)
            };
            
            console.log('Calculando com inputs:', inputs);
            const result = bankSelector.findBestBankConfiguration(inputs);
            console.log('Resultado do cálculo:', result);
            
            const resultsDiv = document.getElementById('circuit-results');
            if (result.error) {
                resultsDiv.innerHTML = `<p style="color: red;">Erro: ${result.error}</p>`;
            } else {
                resultsDiv.innerHTML = `<h3>Soluções encontradas:</h3>`;
                result.solutions.forEach((sol, i) => {
                    resultsDiv.innerHTML += `
                        <div style="border: 1px solid #ccc; margin: 5px; padding: 10px;">
                            <strong>Solução ${i+1}:</strong><br>
                            Configuração: ${sol.q_config_str}<br>
                            Potência: ${sol.q_efetiva.toFixed(2)} MVAr<br>
                            Colunas: ${sol.columns_activated_str || 'N/A'}<br>
                            CS: ${sol.csToClose.join(', ')}<br>
                            Q: ${sol.qToClose.join(', ')}
                        </div>
                    `;
                });
            }
        });
    </script>
</body>
</html>
