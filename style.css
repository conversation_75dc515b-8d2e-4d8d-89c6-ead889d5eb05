/* ======================================================= */
/* DIAGRAMA UNIFILAR - SISTEMA DE CONTROLE DE CAPACITORES  */
/* Layout Profissional - V2.14 (Padrão de Cores Brasileiro)*/
/* ======================================================= */

/* --- Paleta de Cores com Padrão Brasileiro (R/S/T) --- */
:root {
    /* Cores de Estado do Circuito */
    --color-off: #8d99ae;             /* Cinza: Desenergizado */
    --color-potential: #fca311;       /* Laranja: Energizado (sem corrente) */
    --color-phase-a: #E74C3C;         /* Vermelho: Fase A (R) */
    --color-phase-b: #198754;         /* Verde: Fase B (S) */
    --color-phase-c: #0d6efd;         /* Azul: Fase C (T) */
    --color-danger: #dc3545;          /* Vermelho (para Curto-Circuito) */

    /* Cores das Chaves (conforme legenda e função) */
    --color-chave-principal: #d63384;   /* Magenta */
    --color-chave-manobra: #fca311;     /* Laranja */
    --color-chave-delta: #e67e22;       /* Laranja Escuro */
    --color-chave-wye: #1abc9c;         /* Turquesa */
    
    /* Cores da UI */
    --color-ui-primary: #0d6efd;       /* Azul para botões e foco */
    --color-background: #edf2f4;
    --color-panel-background: #ffffff;
    --color-text-dark: #2b2d42;
    --color-border-light: #dee2e6;
}

/* --- ESTILOS GERAIS E FUNDO --- */
body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--color-background);
    overflow: hidden;
    color: var(--color-text-dark);
    display: flex;
    height: 100vh;
}

/* --- CONTAINER PRINCIPAL --- */
#main-container {
    display: flex;
    width: 100%;
    height: 100vh;
}

/* --- CONTAINER DO DIAGRAMA --- */
#diagram-container {
    position: relative;
    transform-origin: top left;
    width: 2400px;
    height: 900px;
    cursor: grab;
    flex: 1;
    overflow: hidden;
    margin-left: 350px;
    transition: margin-left 0.3s ease-in-out;
    padding: 10px;
}

body:has(#diagram-container:active) {
    cursor: grabbing;
}

/* --- COMPONENTES PADRÃO --- */
.bus,
.vertical-line,
.horizontal-line,
.connection-line,
.connection-dot {
    background-color: var(--color-off);
    position: absolute;
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.bus { height: 5px; z-index: 1; border-radius: 2.5px; }
.vertical-line { width: 5px; z-index: 2; }
.horizontal-line { height: 5px; z-index: 1; }

/* ======================================================= */
/* ESTADOS VISUAIS DO CIRCUITO (CORES BR)                  */
/* ======================================================= */

@keyframes flow-horizontal { to { background-position: -200px 0; } }
@keyframes flow-vertical { to { background-position: 0 -200px; } }

@keyframes interphase-pulse {
    0% { filter: brightness(1.1); }
    50% { filter: brightness(1.5); box-shadow: 0 0 8px var(--color-danger); }
    100% { filter: brightness(1.1); }
}

/* CORES DE BASE COM PADRÃO BRASILEIRO */
.has-potential { background-color: var(--color-potential) !important; }
.is-active-a { background-color: var(--color-phase-a) !important; }
.is-active-b { background-color: var(--color-phase-b) !important; }
.is-active-c { background-color: var(--color-phase-c) !important; }

.interphase-flow {
    background-color: var(--color-danger) !important;
    background-image: none !important;
    animation: interphase-pulse 0.8s ease-in-out infinite !important;
}

/* ANIMAÇÃO DE FLUXO (BRANCA) */
.line.animated-flow, .bus.animated-flow {
    background-image: linear-gradient(to right, transparent, transparent 45%, rgba(255,255,255,0.7) 50%, transparent 55%, transparent) !important;
    background-size: 200px 100% !important;
    animation: flow-horizontal 2s linear infinite;
}
.vertical-line.animated-flow {
     background-image: linear-gradient(to bottom, transparent, transparent 45%, rgba(255,255,255,0.7) 50%, transparent 55%, transparent) !important;
     background-size: 100% 200px !important;
     animation-name: flow-vertical;
}

/* ======================================================= */
/* ESTILOS DE COMPONENTES                                  */
/* ======================================================= */

.switch {
    position: absolute; width: 32px; height: 20px; background-color: #fff;
    border: 2px solid #adb5bd; border-radius: 3px; cursor: pointer; z-index: 10;
    transition: all 0.2s ease; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    background-image: linear-gradient(45deg, transparent 45%, var(--color-off) 48%, var(--color-off) 52%, transparent 55%);
}
.switch.closed {
    background-image: linear-gradient(90deg, transparent 45%, var(--color-text-dark) 45%, var(--color-text-dark) 55%, transparent 55%);
    border-color: var(--color-text-dark);
}
.switch:hover { transform: scale(1.1); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); }
.switch-label {
    position: absolute; font-size: 9px; font-weight: 600; color: #000000;
    white-space: nowrap; left: 60%; top: -60%; transform: translateY(-50%);
}
.capacitor {
    position: absolute; width: 48px; height: 24px; background-color: #f8f9fa;
    border: 1px solid #ced4da; border-radius: 2px; transition: all 0.2s ease;
    box-sizing: border-box; cursor: default; z-index: 4;
}
.capacitor-label {
    font-size: 9px; font-weight: bold; color: var(--color-text-dark); position: absolute;
    top: 50%; left: 50%; transform: translate(-50%, -50%);
    white-space: normal; width: 100%; text-align: center; line-height: 1.1;
}

/* Coloração de Chaves (conforme legenda e função) */
.switch.magenta { border-color: var(--color-chave-principal); }
.switch.yellow { border-color: var(--color-chave-manobra); }
.switch.delta-switch { border-color: var(--color-chave-delta); }
.switch.wye-switch { border-color: var(--color-chave-wye); }

/* Coloração de Componentes */
.switch.has-potential, .capacitor.has-potential { border-color: var(--color-potential) !important; }
.capacitor.has-potential { background-color: #fffbeb; }

.switch.is-active-a, .capacitor.is-active-a { border-color: var(--color-phase-a) !important; }
.capacitor.is-active-a { background-color: #fbe6e8; }

.switch.is-active-b, .capacitor.is-active-b { border-color: var(--color-phase-b) !important; }
.capacitor.is-active-b { background-color: #e8f3ee; }

.switch.is-active-c, .capacitor.is-active-c { border-color: var(--color-phase-c) !important; }
.capacitor.is-active-c { background-color: #e7f0ff; }

.switch.interphase-flow, .capacitor.interphase-flow { border-color: var(--color-danger) !important; border-width: 2px !important; }
.capacitor.interphase-flow { background-color: #fbe6e8; }
.capacitor.interphase-flow .capacitor-label { color: #87131f; font-weight: 900; }

.fonte-seta {
    position: absolute; z-index: 10; transition: all 0.3s; cursor: pointer;
    border-bottom-color: var(--color-off) !important;
}
.fonte-seta:hover { filter: brightness(1.3); }
.fonte-seta.energized {
    border-bottom-color: #28a745 !important;
    filter: drop-shadow(0 0 8px #28a745);
}
.no-connection-symbol {
    position: absolute; width: 8px; height: 4px; border: 2px solid var(--color-off);
    border-bottom: none; border-radius: 8px 8px 0 0; background-color: var(--color-background);
    z-index: 10;
}
.phase-label {
    position: absolute; font-weight: bold; font-size: 18px; color: #34495e;
    text-transform: uppercase; letter-spacing: 1px;
}
.ground-symbol { position: absolute; font-size: 20px; color: var(--color-text-dark); z-index: 10; }

/* ======================================================= */
/* PAINÉIS DE INTERFACE                                    */
/* ======================================================= */

#total-power-display {
    position: fixed;
    top: 15px;
    right: 20px;
    z-index: 2000;
    background: var(--color-panel-background);
    padding: 8px 15px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
    color: var(--color-text-dark);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--color-border-light);
}

#logo-container {
    position: fixed;
    top: 15px;
    left: 370px;
    z-index: 2000;
    transition: left 0.3s ease-in-out;
}

body:has(#config-panel.minimized) #logo-container {
    left: 50px;
}

body:has(#config-panel.minimized) #diagram-container {
    margin-left: 30px;
}

#logo-image {
    height: 50px; object-fit: contain; border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); background: #fff; padding: 5px;
}
#config-panel {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 350px;
    background: var(--color-panel-background);
    border-right: 2px solid var(--color-ui-primary);
    padding: 15px 20px;
    z-index: 2000;
    box-shadow: 5px 0 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 20px;
    font-size: 12px;
    overflow-y: auto;
    transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;
}

#config-panel.minimized {
    transform: translateX(-320px);
    width: 30px;
    padding: 15px 5px;
    overflow: hidden;
}
.minimize-button {
    position: absolute;
    top: 15px;
    right: 10px;
    background: var(--color-ui-primary);
    color: white;
    border: none;
    border-radius: 3px;
    width: 25px;
    height: 25px;
    font-size: 14px;
    line-height: 1;
    cursor: pointer;
    z-index: 2001;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.minimize-button:hover {
    background: #0b5ed7;
}

#config-panel.minimized .minimize-button {
    right: 2px;
    transform: rotate(180deg);
}
.panel-column {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
    margin-bottom: 20px;
}
#config-panel h3 {
    margin: 0 0 3px 0; color: var(--color-text-dark); font-size: 12px;
    font-weight: 600; text-transform: uppercase; border-bottom: 2px solid var(--color-border-light);
    padding-bottom: 8px;
}
.config-row { display: flex; flex-direction: column; gap: 5px; }
.config-row label { font-weight: 600; color: #495057; font-size: 12px; }
.config-row select, .config-row input {
    padding: 8px 9px; border: 1px solid var(--color-border-light); border-radius: 4px;
    font-size: 12px; background: #f8f9fa; transition: border-color 0.2s ease, box-shadow 0.2s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
.config-row select:focus, .config-row input:focus {
    outline: none; border-color: var(--color-ui-primary); box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

.action-button {
    padding: 8px 12px;
    background: var(--color-ui-primary);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 5px;
    width: 100%;
}

.action-button:hover {
    background: #0b5ed7;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button:active {
    transform: translateY(0);
}


.calc-button {
    padding: 10px 12px; background: var(--color-ui-primary); color: white; border: none;
    border-radius: 4px; font-weight: bold; font-size: 12px; cursor: pointer;
    transition: all 0.2s ease; text-transform: uppercase; margin-top: auto;
    display: flex; align-items: center; justify-content: center; gap: 8px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
.calc-button:hover { background: #0b5ed7; transform: translateY(-2px); box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3); }
#circuit-results, #diagram-status-display, #legend {
    background: #f8f9fa; border: 1px solid var(--color-border-light); border-radius: 4px;
    padding: 12px; min-height: 60px; font-size: 12px;
    color: #495057; line-height: 1.5; flex-grow: 1;
}
#circuit-results table { font-size: 12px; }
#circuit-results tr.applied-config { background-color: #d1e7dd !important; border-left: 4px solid #0f5132; }
#circuit-results tr.applied-config td { font-weight: bold; color: #0f5132; }
.apply-button {
    font-size: 12px; padding: 4px 8px; cursor: pointer; border-radius: 4px;
    border: 1px solid var(--color-ui-primary); background: #eaf2ff; color: var(--color-ui-primary);
    font-weight: bold; transition: all 0.2s ease;
}
.apply-button:hover { background: var(--color-ui-primary); color: white; transform: scale(1.05); }

.status-table { 
    width: 100%; 
    border-collapse: collapse; 
    font-size: 12px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
.status-table td { padding: 5px 6px; border-bottom: 1px solid #f0f2f5; vertical-align: middle; }
.status-table tr:last-child td { border-bottom: none; }
.status-table td:first-child { font-weight: 600; color: #495057; width: 60%; }
.status-table td:last-child { text-align: right; font-family: 'Consolas', 'Courier New', monospace; font-weight: normal; }
.status-warning { color: #f39c12; font-weight: bold !important; }
.status-danger { color: var(--color-danger); font-weight: bold !important; }
.status-columns-container { display: flex; gap: 15px; width: 100%; }
.status-column { flex: 1; min-width: 0; }

/* LEGENDA NO PAINEL */
#legend ul { list-style: none; padding: 0; margin: 0; }
#legend li { display: flex; align-items: center; margin-bottom: 5px; }
#legend li:last-child { margin-bottom: 0; }
#legend hr { border: none; border-top: 1px solid var(--color-border-light); margin: 8px 0; }
.swatch { width: 14px; height: 14px; border-radius: 3px; margin-right: 8px; border: 1px solid rgba(0,0,0,0.2); flex-shrink: 0; }
.interphase-flow-legend {
    width: 14px; height: 14px; border-radius: 3px; margin-right: 8px;
    border: 1px solid rgba(0,0,0,0.2); background-color: var(--color-danger);
    animation: interphase-pulse 0.8s ease-in-out infinite;
}
.swatch-switch {
    width: 18px; height: 11px; border-radius: 2px; margin-right: 8px;
    border: 2px solid; flex-shrink: 0;
}
.swatch-switch.magenta { border-color: var(--color-chave-principal); }
.swatch-switch.yellow { border-color: var(--color-chave-manobra); }
.swatch-switch.delta { border-color: var(--color-chave-delta); }
.swatch-switch.wye { border-color: var(--color-chave-wye); }